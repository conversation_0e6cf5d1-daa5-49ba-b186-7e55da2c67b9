# SSH Configuration Template for Git
# Place this in ~/.ssh/config

# GitHub configuration
Host github.com
    HostName github.com
    User git
    IdentityFile ~/.ssh/id_rsa_git
    IdentitiesOnly yes
    AddKeysToAgent yes

# GitLab configuration
Host gitlab.com
    HostName gitlab.com
    User git
    IdentityFile ~/.ssh/id_rsa_git
    IdentitiesOnly yes
    AddKeysToAgent yes

# Bitbucket configuration
Host bitbucket.org
    HostName bitbucket.org
    User git
    IdentityFile ~/.ssh/id_rsa_git
    IdentitiesOnly yes
    AddKeysToAgent yes

# Custom Git server example
# Host myserver
#     HostName git.mycompany.com
#     User git
#     Port 22
#     IdentityFile ~/.ssh/id_rsa_git
#     IdentitiesOnly yes
#     AddKeysToAgent yes

# General SSH settings
Host *
    AddKeysToAgent yes
    UseKeychain yes
    ServerAliveInterval 60
    ServerAliveCountMax 3
