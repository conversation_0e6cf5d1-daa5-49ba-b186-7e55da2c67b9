#!/bin/bash

# SSH Key Generation Script for Git
# This script generates an SSH key pair for use with Git repositories

echo "🔑 SSH Key Generator for Git"
echo "=============================="

# Get user email for the SSH key
read -p "Enter your email address for the SSH key: " email

if [ -z "$email" ]; then
    echo "❌ Email is required!"
    exit 1
fi

# Set key filename
KEY_NAME="id_rsa_git"
SSH_DIR="$HOME/.ssh"

# Create .ssh directory if it doesn't exist
mkdir -p "$SSH_DIR"
chmod 700 "$SSH_DIR"

# Generate SSH key pair
echo "🔧 Generating SSH key pair..."
ssh-keygen -t rsa -b 4096 -C "$email" -f "$SSH_DIR/$KEY_NAME" -N ""

if [ $? -eq 0 ]; then
    echo "✅ SSH key pair generated successfully!"
    echo ""
    echo "📁 Files created:"
    echo "   Private key: $SSH_DIR/$KEY_NAME"
    echo "   Public key:  $SSH_DIR/$KEY_NAME.pub"
    echo ""
    
    # Display the public key
    echo "🔓 Your SSH Public Key:"
    echo "======================="
    cat "$SSH_DIR/$KEY_NAME.pub"
    echo ""
    
    # Copy to clipboard if possible
    if command -v pbcopy >/dev/null 2>&1; then
        cat "$SSH_DIR/$KEY_NAME.pub" | pbcopy
        echo "📋 Public key copied to clipboard!"
    elif command -v xclip >/dev/null 2>&1; then
        cat "$SSH_DIR/$KEY_NAME.pub" | xclip -selection clipboard
        echo "📋 Public key copied to clipboard!"
    fi
    
    echo ""
    echo "📝 Next Steps:"
    echo "1. Copy the public key above"
    echo "2. Go to your Git provider (GitHub, GitLab, etc.)"
    echo "3. Add the public key to your account settings"
    echo "4. Test the connection with: ssh -T **************"
    echo ""
    echo "🔧 To use this key with Git:"
    echo "   git config --global user.email \"$email\""
    echo "   ssh-add $SSH_DIR/$KEY_NAME"
    
else
    echo "❌ Failed to generate SSH key!"
    exit 1
fi
