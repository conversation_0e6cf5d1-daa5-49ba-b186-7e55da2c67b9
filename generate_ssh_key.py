#!/usr/bin/env python3
"""
SSH Key Generator for Git
Generates an SSH key pair for use with Git repositories
"""

import os
import subprocess
import sys
from pathlib import Path

def run_command(command, shell=False):
    """Run a command and return the result"""
    try:
        result = subprocess.run(command, shell=shell, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def copy_to_clipboard(text):
    """Copy text to clipboard if possible"""
    try:
        # macOS
        if sys.platform == "darwin":
            subprocess.run(["pbcopy"], input=text, text=True, check=True)
            return True
        # Linux
        elif sys.platform == "linux":
            subprocess.run(["xclip", "-selection", "clipboard"], input=text, text=True, check=True)
            return True
        # Windows
        elif sys.platform == "win32":
            subprocess.run(["clip"], input=text, text=True, check=True, shell=True)
            return True
    except:
        pass
    return False

def main():
    print("🔑 SSH Key Generator for Git")
    print("==============================")
    
    # Get user email
    email = input("Enter your email address for the SSH key: ").strip()
    if not email:
        print("❌ Email is required!")
        sys.exit(1)
    
    # Set up paths
    ssh_dir = Path.home() / ".ssh"
    key_name = "id_rsa_git"
    private_key_path = ssh_dir / key_name
    public_key_path = ssh_dir / f"{key_name}.pub"
    
    # Create .ssh directory
    ssh_dir.mkdir(mode=0o700, exist_ok=True)
    
    # Generate SSH key
    print("🔧 Generating SSH key pair...")
    
    command = [
        "ssh-keygen",
        "-t", "rsa",
        "-b", "4096",
        "-C", email,
        "-f", str(private_key_path),
        "-N", ""  # No passphrase
    ]
    
    success, stdout, stderr = run_command(command)
    
    if success:
        print("✅ SSH key pair generated successfully!")
        print()
        print("📁 Files created:")
        print(f"   Private key: {private_key_path}")
        print(f"   Public key:  {public_key_path}")
        print()
        
        # Read and display public key
        try:
            with open(public_key_path, 'r') as f:
                public_key = f.read().strip()
            
            print("🔓 Your SSH Public Key:")
            print("=======================")
            print(public_key)
            print()
            
            # Try to copy to clipboard
            if copy_to_clipboard(public_key):
                print("📋 Public key copied to clipboard!")
            else:
                print("📋 Could not copy to clipboard automatically")
            
            print()
            print("📝 Next Steps:")
            print("1. Copy the public key above")
            print("2. Go to your Git provider (GitHub, GitLab, etc.)")
            print("3. Add the public key to your account settings")
            print("4. Test the connection with: ssh -T **************")
            print()
            print("🔧 To use this key with Git:")
            print(f'   git config --global user.email "{email}"')
            print(f"   ssh-add {private_key_path}")
            
        except Exception as e:
            print(f"❌ Error reading public key: {e}")
            
    else:
        print("❌ Failed to generate SSH key!")
        print(f"Error: {stderr}")
        sys.exit(1)

if __name__ == "__main__":
    main()
